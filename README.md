# Scribe2SRT v0.3.3

一个基于 ElevenLabs Speech-to-Text API 的音视频转字幕工具，支持多语言转录和专业字幕制作标准。

## 🚀 主要特性

### 全新的两阶段字幕分割算法
- **句子预分割**：基于标点符号优先级的语义分割
- **智能合并**：基于CPS、CPL、显示时间等规则的优化合并
- **语义完整性**：避免破坏句子结构，提升可读性

### 显著的质量改善

| 指标 | v0.2.x | v0.3.x | 改善幅度 |
|------|--------|--------|----------|
| 标点问题率 | 7.72% | 2.81% | **+4.91%** ✅ |
| 时长过短率 | 5.30% | 0.64% | **+4.66%** ✅ |
| CPS过高率 | 10.13% | 7.02% | **+3.11%** ✅ |
| 整体合规率 | 75.41% | 79.11% | **+3.71%** ✅ |
| 标点结尾率 | - | 97.7% | **显著提升** ✅ |

## 功能特点

- 🎯 **高质量转录**：基于 ElevenLabs 先进的语音识别技术
- 🌍 **多语言支持**：支持中文、英文、日文等多种语言
- 📝 **专业字幕标准**：遵循 Netflix 等行业标准的字幕制作规范
- ⚡ **智能分割**：基于标点符号优先级的语义分割算法
- 🔄 **智能重试机制**：失败时自动保留临时文件，重试时快速恢复
- 🎨 **用户友好界面**：简洁直观的图形用户界面，统一的视觉设计
- 📊 **实时进度反馈**：清晰的进度显示和状态提示
- 🛠️ **质量分析**：内置字幕质量检测和优化建议

## 安装要求

### 必需依赖
- Python 3.8+
- PySide6 (Qt界面库)
- requests (网络请求)
- requests-toolbelt (文件上传)

### 可选依赖
- FFmpeg (推荐安装，用于视频文件处理)

## 快速开始

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/scribe2srt.git
   cd scribe2srt
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行程序**
   ```bash
   python app.py
   ```

## 使用方法

1. **选择文件**：拖拽或点击选择音视频文件
2. **设置选项**：选择源语言和其他处理选项
3. **开始转录**：点击"生成字幕"按钮
4. **获取结果**：程序会自动生成 SRT 字幕文件

## 支持的文件格式

### 音频格式
- MP3, WAV, FLAC, M4A, AAC, OGG

### 视频格式
- MP4, MOV, MKV, AVI, FLV, WEBM

### 转录数据
- JSON (ElevenLabs 格式)

## 字幕质量标准

本工具遵循以下专业字幕制作标准：

- **时长控制**：最短 0.83 秒，最长 7.0 秒
- **字符密度**：CJK 语言每秒最多 15 字符，拉丁语言每秒最多 15 字符
- **行长限制**：CJK 语言每行最多 25 字符，拉丁语言每行最多 42 字符
- **语义完整性**：优先保持句子完整，基于标点符号优先级分割

## 高级功能

### 字幕设置
- 自定义时长和间隔参数
- 调整字符密度限制
- 设置每行字符数限制

### 并发处理
- 支持大文件分段并发处理
- 可配置并发数和重试策略
- API 速率限制控制

### 智能重试机制
- 失败时自动保留临时文件
- 重试时避免重复音频提取
- 完整的状态恢复和进度显示
- 智能错误处理和异常恢复

### 质量分析
- 自动检测字幕质量问题
- 提供详细的合规性报告
- 生成改进建议

## 项目结构

```
scribe2srt/
├── app.py                    # 主应用入口
├── requirements.txt          # 依赖文件
├── README.md                # 项目文档
├── settings.json            # 配置文件
├── core/                    # 核心功能模块
│   ├── __init__.py         # 包初始化
│   ├── config.py           # 配置管理
│   ├── srt_processor.py    # 字幕处理器
│   ├── sentence_splitter.py # 句子分割器
│   ├── intelligent_merger.py # 智能合并器
│   ├── ffmpeg_utils.py     # FFmpeg工具
│   ├── async_chunk_processor.py # 异步处理
│   └── worker.py           # 工作线程
├── ui/                     # 用户界面模块
│   ├── __init__.py         # 包初始化
│   ├── main_window.py      # 主窗口
│   ├── settings_dialog.py  # 设置对话框
│   ├── async_settings_dialog.py # 异步设置对话框
│   ├── widgets.py          # 自定义组件
│   └── segmented_progress_bar.py # 进度条
├── api/                    # API客户端模块
│   ├── __init__.py         # 包初始化
│   └── client.py           # API客户端
├── tests/                  # 测试模块
│   ├── __init__.py         # 包初始化
│   ├── test_subtitle_rules.py
│   └── optimize_based_on_analysis.py
├── sample/                 # 示例文件
└── docs/                   # 文档
    └── 字幕制作的通用原则.md
```

## 技术特点

### 新一代字幕分割算法
- **两阶段处理**：句子预分割 + 智能合并
- **标点符号优先**：基于语言学规律的分割策略
- **语义完整性**：避免破坏句子结构
- **多语言优化**：针对 CJK 和拉丁语言的差异化处理

### 质量保证
- **实时质量检测**：处理过程中自动检测质量问题
- **合规性验证**：确保符合行业标准
- **智能优化**：自动调整参数以提高质量

## 核心改进

### 算法优化
- **移除长停顿检测**：用基于标点符号的语义分割替代时间间隔检测
- **智能合并策略**：基于CPS、CPL、显示时间等多规则约束
- **语义完整性**：显著减少句子中断，提升可读性
- **模块化架构**：重新组织代码结构，提高可维护性

### 技术创新
- **标点符号优先级**：高优先级（。！？）、中优先级（；：）、低优先级（，、）
- **动态CPS调整**：根据文本长度智能调整字符密度限制
- **智能收益计算**：指导合并决策的收益评估算法
- **智能重试机制**：失败时保留状态，重试时快速恢复
- **实时进度反馈**：统一的进度显示和状态管理

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v0.3.3 (最新)
- 🔄 重试机制全面优化：修复临时文件管理，重试时不再重复提取音频
- 📊 进度显示全面改进：修复重试进度显示，优化信息展示层次
- 🎨 界面优化：简化进度条颜色方案，统一视觉风格
- 🛠️ 错误处理增强：修复多个稳定性问题，提升程序健壮性

### v0.3.1
- 🚀 全新的两阶段字幕分割算法
- 📊 显著提升标点符号分割准确率（从 7.72% 降至 2.81%）
- 🏗️ 重构代码架构，模块化设计
- 📈 整体合规率从 75.41% 提升至 79.11%

### v0.2.x
- 基础转录功能
- 简单的字幕分割算法
- 基本的用户界面
